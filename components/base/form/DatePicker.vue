<template>
	<VueDatePicker v-model="dateValue" :model-value="dateValue" @update:model-value="handleDate" v-bind="$attrs" :clearable="false" :auto-apply="true" :enable-time-picker="false" :locale="lang.get('locale')" :format="format" hide-input-icon autocomplete="off" />
	{{ dateValue }}
</template>

<script setup>
	import VueDatePicker from '@vuepic/vue-datepicker';
	const model = defineModel();

	const lang = useLang();
	//const dateValue = ref(model.value ? new Date(model.value) : null);
	const dateValue = ref(new Date());

	const format = date => {
		const day = date.getDate();
		const month = date.getMonth() + 1;
		const year = date.getFullYear();
		return `${day}.${month}.${year}`;
	};

	function handleDate(newDate) {
		console.log('test', newDate);
		model.value = format(newDate);
	}
</script>
